import { Divider, Text } from '@mantine/core';
import type { CartItemType } from '@/libs/cart/types';
import type { PromoType } from '@/types/common';
import { CartVendorPromotionDisplay } from './CartVendorPromotionDisplay';
import { OfferType } from '@/types';

export type BuyXGetYPromotionData = {
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
  freeOffer: OfferType;
  promotion: PromoType;
  items: CartItemType[];
  imageUrl: string;
  manufacturer: string;
};

type CartVendorPromoItemProps = {
  promoItem: BuyXGetYPromotionData;
};

export const CartVendorPromoItem = ({
  promoItem,
}: CartVendorPromoItemProps) => {
  if (!promoItem) return null;

  return (
    <div className="flex w-full rounded-lg p-4">
      <div className="relative h-32 w-60 overflow-hidden rounded-lg border border-solid border-[#f2f2f2] bg-white p-0">
        <img
          src={promoItem.imageUrl}
          alt=""
          className="absolute top-[calc(50%+1rem)] left-1/2 max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 object-scale-down data-[fallback=true]:top-1/2"
        />
      </div>
      <div className="flex-1">
        <h3 className="max-w-9/10 text-sm leading-5 font-medium text-black">
          {promoItem.promotion.name}
        </h3>
        <div className="m-1 mb-3 flex text-center">
          <span className="text-xs text-neutral-500/80">
            SKU:
            <span className="ml-0.5 text-xs font-medium text-[#333333]">
              {promoItem.freeOffer.vendorSku}
            </span>
          </span>
          {promoItem.manufacturer ? (
            <>
              <Divider orientation="vertical" h="1rem" mx="md" />
              <Text ml="2px" c="#344054" size="xs">
                {promoItem.manufacturer}
              </Text>
            </>
          ) : null}
        </div>
        <div className="divider-h"></div>
        {promoItem.items.map((item) => (
          <CartVendorPromotionDisplay key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};
