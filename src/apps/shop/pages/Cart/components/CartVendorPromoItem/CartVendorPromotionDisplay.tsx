import { Badge } from '@/libs/ui/Badge/Badge';
import { OfferType, ProductType } from '@/types';
import { getPriceString } from '@/utils';
import { getPromotionDetails } from './promotionUtils';
import { CartItemType } from '@/libs/cart/types';

type CartVendorPromotionDisplayProps = {
  item: CartItemType;
};

export const CartVendorPromotionDisplay = ({
  item,
}: CartVendorPromotionDisplayProps) => {
  const detail = getPromotionDetails({
    item,
  });

  if (!detail) return null;

  const { freeItemsQty, freeOffer } = detail;

  return (
    <div className="grid w-full gap-1 py-3">
      <div className="flex items-center gap-2">
        <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
          {item.quantity}
        </Badge>
        <span className="flex-1 text-xs font-medium">{item.product.name}</span>
        <span className="text-xs font-medium text-neutral-500">
          {getPriceString(item.subtotal)}
        </span>
      </div>
      {freeItemsQty > 0 && (
        <div className="flex items-center gap-2">
          <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
            {freeItemsQty}
          </Badge>
          <span className="flex-1 text-xs font-medium">{freeOffer.name}</span>
          <span className="text-xs font-medium text-green-700/90">Free</span>
        </div>
      )}
    </div>
  );
};
